/* 订单记录页面样式 */
.container {
  background-color: #f8f8f8;
  padding-bottom: 20rpx;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  background-color: #fff;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 筛选栏 */
.filter-bar {
  display: flex;
  background-color: #fff;
  padding: 20rpx 40rpx;
  border-bottom: 1rpx solid #eee;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-top: -200rpx;
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 10rpx;
  margin: 0 10rpx;
  transition: all 0.3s ease;
}

.filter-item text {
  font-size: 28rpx;
  color: #666;
}

.filter-item.active {
  background-color: #5e380c;
  color: #fff;
}

.filter-item.active text {
  color: #fff;
  font-weight: bold;
}

/* 订单列表 */
.order-list {
  height: calc(100vh - 200rpx);
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-number {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.order-date {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.order-status.pending {
  background-color: #fff3e0;
  color: #ff9800;
}

.order-status.paid {
  background-color: #e3f2fd;
  color: #2196f3;
}

.order-status.completed {
  background-color: #e8f5e8;
  color: #4caf50;
}

/* 订单内容 */
.order-content {
  margin-bottom: 20rpx;
}

.seat-info {
  margin-bottom: 15rpx;
}

.seat-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.seat-location {
  font-size: 26rpx;
  color: #666;
}

.time-info {
  display: flex;
  align-items: center;
}

.time-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.time-range {
  font-size: 26rpx;
  color: #333;
}

/* 订单底部 */
.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.price-info {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: none;
  min-width: 120rpx;
}

.pay-btn {
  background-color: #ff6b35;
  color: #fff;
}

.cancel-btn {
  background-color: #fff;
  color: #ff4444;
  border: 1rpx solid #ff4444;
}

.detail-btn {
  background-color: #f5f5f5;
  color: #666;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.go-booking-btn {
  background-color: #ff6b35;
  color: #fff;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 30rpx;
  color: #666;
  font-size: 26rpx;
}
