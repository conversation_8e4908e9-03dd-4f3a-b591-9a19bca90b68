.page {
  background-color: rgba(255,255,255,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.block_1 {
  background-color: rgba(248,234,218,1.000000);
  width: 750rpx;
  height: 1624rpx;
  display: flex;
  flex-direction: column;
}
.group_31 {
  position: relative;
  width: 750rpx;
  height: 300rpx;
  display: flex;
  flex-direction: column;
}
.text_1 {
  width: 100rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(94,56,12,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 86rpx 0 0 324rpx;
}
.group_2 {
  background-color: rgba(255,255,255,1.000000);
  position: absolute;
  left: -2rpx;
  top: 132rpx;
  width: 750rpx;
  height: 134rpx;
  flex-direction: row;
  display: flex;
  justify-content: flex-center;
}
.section_1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 12rpx;
  width: 125rpx;
  height: 118rpx;
  display: flex;
  flex-direction: row;
  margin: 6rpx 0 0 16rpx;
  transition: all 0.2s;
  cursor: pointer;
}

.section_1.date-selected {
  background-color: rgba(94,56,12,1.000000) !important;
}
.text-group_4 {
  width: 112rpx;
  height: 88rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 32rpx;
}
.text_2 {
  width: 44rpx;
  height: 24rpx;
  overflow-wrap: break-word;
  color: rgba(102,102,102,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin-left: 20rpx;
  transition: color 0.2s;
}

.text_2.text-selected {
  color: rgba(255,255,255,1.000000) !important;
}
.text_3 {
  width: 112rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 16rpx;
  transition: color 0.2s;
}

.text_3.text-selected {
  color: rgba(255,255,255,1.000000) !important;
}
.text_4 {
  width: 112rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 70rpx 0 0 22rpx;
}
.text_5 {
  width: 112rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 70rpx 0 0 18rpx;
}
.text_6 {
  width: 112rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 70rpx 0 0 16rpx;
}
.text_7 {
  width: 112rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(51,51,51,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 72rpx 0 0 18rpx;
}
.icon_1 {
  height: 32rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAwElEQVR4nN2TMQuCQBiGn9NLDUVCCFuyIYhob29r7cc2udRfqMGhteZaKksUG4xA1LLceuGGG57nPb6PE8AC6NAgKyD99ShNmgH+QCABxr0WniO/hv0gzASeI9Gk4HRJaoGaKug/C1+1+2PM5hBVQqauMB1orHc3LF0pCt7F1BXmkzbZ6vMpHeJsZGDqogD7QVhPYBkZ5NpqDj7fa77AD0KihI8wVMwgilOW22spUCqIkpRhV+Laai1IkyJ3b/QbH1KiUQ/aVKg4AAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 32rpx;
  position: relative;
  margin: 64rpx 16rpx 0 18rpx;
}
.block_2 {
  position: relative;
  width: 26rpx;
  height: 8rpx;
  border: 1.3333333730697632px solid rgba(94,56,12,1);
  display: flex;
  flex-direction: column;
  margin: 6rpx 0 0 2rpx;
}
.group_3 {
  position: absolute;
  left: 8rpx;
  top: -2rpx;
  width: 2rpx;
  height: 6rpx;
  border: 1.3333333730697632px solid rgba(94,56,12,1);
  display: flex;
  flex-direction: column;
}
.group_4 {
  position: absolute;
  left: 18rpx;
  top: -2rpx;
  width: 2rpx;
  height: 6rpx;
  border: 1.3333333730697632px solid rgba(94,56,12,1);
  display: flex;
  flex-direction: column;
}
.block_3 {
  width: 35rpx;
  height: 35rpx;
  position: relative;
  top: 80rpx;
  left: 15rpx;
}
.group_5 {
  background-color: rgba(255,252,247,1.000000);
  border-radius: 12rpx;
  position: relative;
  width: 702rpx;
  height: 982rpx;
  display: flex;
  flex-direction: column;
  margin: -2rpx 0 0 30rpx;
}
.box_1 {
  background-color: rgba(246,208,216,1.000000);
  height: 84rpx;
  width: 652rpx;
  display: flex;
  flex-direction: column;
  margin: 14rpx 0 0 32rpx;
}
.group_32 {
  width: 572rpx;
  height: 38rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 6rpx 0 0 40rpx;
}
.box_3 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text-wrapper_1 {
  height: 38rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_8 {
  width: 14rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.block_10 {
  width: 508rpx;
  height: 38rpx;
  flex-direction: row;
  display: flex;
}
.text-wrapper_2 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_9 {
  width: 14rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_3 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 12rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_10 {
  width: 14rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_4 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_11 {
  width: 14rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_5 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_12 {
  width: 14rpx;
  height: 38rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_6 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 12rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_13 {
  width: 14rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: -2rpx 0 0 20rpx;
}
.text-wrapper_7 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_14 {
  width: 14rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_8 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 12rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_15 {
  width: 14rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: -2rpx 0 0 20rpx;
}
.text-wrapper_9 {
  background-color: rgba(104,102,98,1.000000);
  height: 38rpx;
  margin-left: 10rpx;
  display: flex;
  flex-direction: column;
  width: 54rpx;
}
.text_16 {
  width: 14rpx;
  height: 30rpx;
  overflow-wrap: break-word;
  color: rgba(233,245,232,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: -2rpx 0 0 20rpx;
}
.group_33 {
  width: 70rpx;
  height: 20rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 2rpx 0 4rpx 40rpx;
  position: relative;
  left: 245rpx;
  top: 10rpx;
}
.group_6 {
  height: 34rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAYAAAA7bUf6AAAA+0lEQVR4nGNkYGBQgGKKwAIGBob/lGAmSl3AwMDAwMLAwMAQGuzPICwsTJYBM2bNgxjCxsbOcPnKVYb7Dx6RZICjvS3CJQwMDAxv3rxjePbsOV5NifHRDOzs7AwzZs1DEWfBoR4r2Ll7H1ZxDENgtuEDBF3Czs7OcObseYbPn79gNcDE2AC3S9jZ2Bh4eXkYGBgYGH7//s3w8dMnrIZ8+/4drg7DECsrcwYrK3MGBgYGBksLM+z+gILoyDAMsQUMtE6xocH+DArycnjVYDXEz9eTwdEBkpDY2NjhsZWRlsQgLCyEoR5rOrl58w7Dr1+/MMVvYRdnYBgsuRgAhudqM0jXqx0AAAAASUVORK5CYII=) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 34rpx;
}
.box_6 {
  width: 34rpx;
  height: 34rpx;
  display: flex;
  flex-direction: column;
}
.text_17 {
  width: 66rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 25rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
}
.box_23 {
  width: 332rpx;
  height: 50rpx;
  flex-direction: row;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0 10rpx;
  left: 140rpx;
}
.group_7 {
  background-color: rgba(0,0,0,1.000000);
  height: 50rpx;
  display: flex;
  flex-direction: column;
  width: 152rpx;
  position: relative;
}
.box_8 {
  width: 114rpx;
  height: 2rpx;
  border: 1.3333333730697632px solid rgba(51,51,51,1);
  display: flex;
  flex-direction: column;
  margin: 24rpx 0 0 18rpx;
}
.box_9 {
  position: absolute;
  left: 18rpx;
  top: 12rpx;
  width: 38rpx;
  height: 26rpx;
  border: 1.3333333730697632px solid rgba(51,51,51,1);
  display: flex;
  flex-direction: column;
}
.box_10 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 152rpx;
  height: 50rpx;
  display: flex;
  flex-direction: column;
}
.text_18 {
  width: 106rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
  flex: 1;
  margin: 0;
}
.corridor_left_image {
  width: 60rpx;
  height: 30rpx;
  flex-shrink: 0;
}
.corridor_right_image {
  width: 60rpx;
  height: 30rpx;
  flex-shrink: 0;
}
.corridor_spacer {
  width: 60rpx;
  height: 30rpx;
  flex-shrink: 0;
}

/* 座位状态样式 */
.seat-available {
  background-color: rgba(255,255,255,1.000000) !important;
  border: 0.5px solid rgba(225,204,177,1) !important;
}

.seat-selected {
  background-color: rgba(94,56,12,1.000000) !important;
  border: 0.5px solid rgba(94,56,12,1.000000) !important;
}

.seat-selected text {
  color: rgba(255,255,255,1.000000) !important;
}

.seat-occupied {
  background-color: rgba(5,88,42,1.000000) !important;
  border: 0.5px solid rgba(5,88,42,1.000000) !important;
}

.seat-occupied text {
  color: rgba(255,255,255,1.000000) !important;
}

/* 横向日历样式 */
.calendar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.calendar-popup {
  background-color: white;
  border-radius: 20rpx;
  width: 700rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.horizontal-calendar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 110rpx;
  height: 120rpx;
  margin-right: 15rpx;
  border-radius: 12rpx;
  background-color: white;
  transition: all 0.2s;
  cursor: pointer;
}

/* 选中状态 - 棕底白字 */
.date-item.selected {
  background-color: rgba(94,56,12,1.000000) !important;
}

.date-item.selected .date-weekday {
  color: white !important;
}

.date-item.selected .date-number {
  color: white !important;
}

/* 未选中状态 - 白底黑字 */
.date-weekday {
  font-size: 24rpx;
  color: rgba(102,102,102,1.000000);
  margin-bottom: 8rpx;
  font-weight: 500;
  transition: color 0.2s;
}

.date-number {
  font-size: 32rpx;
  color: rgba(51,51,51,1.000000);
  font-weight: bold;
  transition: color 0.2s;
}

.calendar-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(94,56,12,1.000000);
  border-radius: 8rpx;
  margin-left: 10rpx;
}

.calendar-icon-text {
  font-size: 32rpx;
  color: white;
}

/* 日历头部样式 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  background: linear-gradient(135deg, rgba(94,56,12,1.000000), rgba(120,80,40,1.000000));
  color: white;
}

.calendar-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

.calendar-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255,255,255,0.2);
  border-radius: 50%;
  font-size: 32rpx;
  color: white;
  transition: all 0.2s;
}

.calendar-nav:active {
  background-color: rgba(255,255,255,0.3);
  transform: scale(0.95);
}

/* 星期标题样式 */
.calendar-weekdays {
  display: flex;
  background-color: rgba(248,248,248,1.000000);
  padding: 20rpx 0;
}

.weekday {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: rgba(102,102,102,1.000000);
  font-weight: 500;
}

/* 日期网格样式 */
.calendar-days {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  background-color: white;
}

.calendar-day {
  width: calc(100% / 7);
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  transition: all 0.2s;
  cursor: pointer;
}

/* 当月日期 */
.calendar-day.current-month {
  color: rgba(51,51,51,1.000000);
  font-weight: 500;
}

.calendar-day.current-month:hover {
  background-color: rgba(94,56,12,0.1);
}

/* 其他月份日期 */
.calendar-day.other-month {
  color: rgba(200,200,200,1.000000);
  font-weight: normal;
}

/* 今天样式 */
.calendar-day.today {
  background-color: rgba(94,56,12,0.2);
  color: rgba(94,56,12,1.000000);
  font-weight: bold;
}

/* 选中日期样式 */
.calendar-day.selected {
  background-color: rgba(94,56,12,1.000000);
  color: white;
  font-weight: bold;
  transform: scale(1.05);
}

/* 日历底部样式 */
.calendar-footer {
  padding: 30rpx;
  background-color: rgba(248,248,248,1.000000);
  border-top: 1rpx solid rgba(230,230,230,1.000000);
}

.calendar-close {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(94,56,12,1.000000);
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  transition: all 0.2s;
}

.calendar-close:active {
  background-color: rgba(80,48,10,1.000000);
  transform: scale(0.98);
}
.box_11 {
  background-color: rgba(253,247,237,1.000000);
  height: 230rpx;
  width: 480rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 28rpx 0 0 36rpx;
  z-index: 5;
}
.group_34 {
  width: 468rpx;
  height: 68rpx;
  margin-top: 20rpx;
  flex-direction: row;
  display: flex;
}
.block_4 {
  background-color: rgba(255,255,255,1.000000);
  position: relative;
  width: 68rpx;
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: row;
}
.text_19 {
  width: 40rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.box_12 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 68rpx;
  height: 68rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_10 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_20 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_11 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_21 {
  width: 38rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_12 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_22 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_13 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_23 {
  width: 46rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_14 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_24 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_15 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_25 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.group_35 {
  width: 468rpx;
  height: 68rpx;
  flex-direction: row;
  display: flex;
}
.text-wrapper_16 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_26 {
  width: 42rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 22rpx;
}
.text-wrapper_17 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_27 {
  width: 46rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_18 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_28 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 22rpx;
}
.text-wrapper_19 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_29 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_20 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_30 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 22rpx;
}
.text-wrapper_21 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_31 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_22 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_32 {
  width: 42rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_55 {
  width: 62rpx;
  height: 22rpx;
  display: flex;
  flex-direction: row;
  margin: 2rpx 0 50rpx 214rpx;
  z-index: 15;
  position: relative;
}
.text_33 {
  width: 62rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.group_10 {
  background-color: rgba(253,247,237,1.000000);
  height: 180rpx;
  display: flex;
  flex-direction: column;
  width: 480rpx;
  position: absolute;
  left: 0rpx;
  top: 168rpx;
  z-index: 10;
}
.grid_2 {
  width: 468rpx;
  height: 136rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
  display: flex;
  flex-direction: row;
}
.text-wrapper_24 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_34 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_25 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_35 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_26 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_36 {
  width: 38rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_27 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_37 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_28 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_38 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_29 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_39 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_30 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_40 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_31 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_41 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_32 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_42 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_33 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_43 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_34 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_44 {
  width: 42rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_35 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_45 {
  width: 48rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 22rpx;
}
.text-wrapper_36 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-right: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_46 {
  width: 42rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 12rpx 0 0 24rpx;
}
.text-wrapper_37 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_47 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.thumbnail_1 {
  position: absolute;
  left: 178rpx;
  top: -12rpx;
  width: 32rpx;
  height: 32rpx;
}
.box_24 {
  width: 640rpx;
  height: 98rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 130rpx 0 0 38rpx;
}
.group_11 {
  background-color: rgba(195,207,221,1.000000);
  height: 98rpx;
  display: flex;
  flex-direction: column;
  width: 480rpx;
}
.block_5 {
  height: 98rpx;
  width: 480rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.section_4 {
  width: 470rpx;
  height: 68rpx;
  margin-top: 2rpx;
  flex-direction: row;
  display: flex;
}
.group_36 {
  width: 268rpx;
  height: 68rpx;
  flex-direction: row;
  display: flex;
}
.text-wrapper_38 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_48 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 26rpx;
}
.text-wrapper_39 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_49 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 22rpx;
}
.text-wrapper_40 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_50 {
  width: 38rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_41 {
  background-color: rgba(255,255,255,1.000000);
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  width: 68rpx;
}
.text_51 {
  width: 42rpx;
  height: 58rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 28rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 40rpx;
  margin: 10rpx 0 0 24rpx;
}
.text-wrapper_42 {
  background-color: rgba(255,255,255,1.000000);
  width: 68rpx;
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.text_52 {
  width: 42rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 6rpx 0 0 16rpx;
}
.text_53 {
  width: 12rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 0 0 6rpx 30rpx;
}
.text-wrapper_43 {
  background-color: rgba(255,255,255,1.000000);
  width: 68rpx;
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -2rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.text_54 {
  width: 42rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 6rpx 0 0 14rpx;
}
.text_55 {
  width: 12rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 0 0 6rpx 30rpx;
}
.text-wrapper_44 {
  background-color: rgba(255,255,255,1.000000);
  width: 68rpx;
  height: 68rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
}
.text_56 {
  width: 42rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 4rpx 0 0 14rpx;
}
.text_57 {
  width: 12rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 0 0 8rpx 28rpx;
}
.text-wrapper_56 {
  width: 62rpx;
  height: 28rpx;
  display: flex;
  flex-direction: row;
  margin: 2rpx 0 2rpx 228rpx;
}
.text_58 {
  width: 62rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
}
.thumbnail_2 {
  position: absolute;
  left: 192rpx;
  top: 70rpx;
  width: 32rpx;
  height: 32rpx;
}
.group_13 {
  width: 98rpx;
  height: 2rpx;
  border: 1px solid rgba(211,186,172,1);
  margin-top: 48rpx;
  display: flex;
  flex-direction: column;
}
.box_14 {
  background-color: rgba(236,206,207,1.000000);
  height: 92rpx;
  display: flex;
  flex-direction: column;
  width: 644rpx;
  margin: 16rpx 0 0 36rpx;
}
.box_15 {
  width: 644rpx;
  height: 92rpx;
  flex-direction: row;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0 20rpx;
}
.image_1 {
  width: 152rpx;
  height: 50rpx;
  margin: 20rpx 0 0 54rpx;
}
.text_59 {
  width: 106rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
  flex: 1;
  margin: 0;
}
.box_16 {
  background-color: rgba(253,247,237,1.000000);
  height: 170rpx;
  display: flex;
  flex-direction: column;
  width: 644rpx;
  margin: 20rpx 0 42rpx 28rpx;
}
.group_14 {
  width: 644rpx;
  height: 170rpx;
  flex-direction: row;
  display: flex;
}
.image-text_3 {
  width: 84rpx;
  height: 118rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 34rpx 0 0 22rpx;
}
.icon_2 {
  height: 78rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASySURBVHgB7VdbT1xVFF5rz+EmFq2k3ttiotVoAgUEWxLTkTRN02jUpBM1CpkLw6VATPwFk2h8M7VFZBhmmIEqaUsffLCx+FJIlDYidDS+1ATbUoMBe/GCyvRclusMc4YZaJk5DJc04Utg1l77W+d8e5+99tobYAN3OVqczkeX6hewjqire+shRYLBJrfdfyeOBdYJdrs9N0fKHmCzGBDLKst2/jMyFh5eyFs3geFwWCkvK3sYEar1tgZYuau8+MuRsR9+T+QhZIBDhxxbZRlKEEWBBMo1JQLjvp6e8aViPB6P4D/NaDc2uE4D0QHdJoAzN27+/XJ/f7+akcBGl6uULPQhAu3hR+TN99B//C+ECh7p6O6+uCiuseZB0rKOU0R1GwNpbnZt12QaZnHRZOGkqP7U133WiDGdJA0Nzlaw0BCPbH+yOB16G5tIonMNLvsriT1Op3MTqNJJjntJ5IivHA7HFt3f3h64wj8fx4cI1JIYZ0ogi9uNBB+xuWlpJm5GizjV4HSWGB5JkjdzMmyN9T+Vk4UdRt+fM7P+2OyDRlitLwPTAq1Wq4REx9nMSjMkGyTqbG1tzdEbPt+xCUkTezkprsf6X3e7HVW60dfXd5On7uuodIT7p3+7us+0wKeffOI1Dt8GJoCAFZFI5Dmj/Ynff4kIQsa7+Ws45sn4o2FqRFWmBQoBr4J5CAvJB5MckhwgRlSTwPK4PtDGDJtAeyDOhzTBj9wOy4CGVJDskWaEEOqcKIz3kYA/IC4WJTArkDELywGhkthUVbGTJzAqgDM2vmdqMszXZJp/V9oCEfFnWBZEPK6+vv4eTrT3488kOGXYFglLDZsz+Wo8GtKECtQd/TEHWSjamag4m+0+AUovm9Gth/fDSxpKIYNIgAfioggHTAv0+YL6Ij4NJsCf8ERHMPiLbuc/XsB1H1+I+hkssNbn88l6u6nOvovJz85F4bg3EPjJtMA5tuzmB11Jh8ppevGWDO8Z7cOHAzdQU21sTgsEB5ezb3S/x2PPJSGOJoS2J70STMDrPTYNFs3K5vASNE0v+gTyvmAwmHQy6fCHzlvI8mKHL9hj+KYmo4OoiA3qV0mhE4kxyzos2Gy27MLCgjdA1d7m7NnNLr0m/8Vb0TkBapeUWzDQ1tYWSfUcriR7BeIXLCJ/zkPven3BoxkLXCk01Tuneda2xJR87u3sfmchZ12P/CS0ymhtJvguKzvfdTtOyhnUDwlFRUX3QoYounx5xjM4qCz0N9XVFmdD9tQRv38K0hVYX1OzTeRlfcDbxB4uO4/BylwNVF6jE7zhe0HcCkUTLg0sEtjsrt2houW8fqaD1cP3/84q+3t7e6+nIiatQS5Fj/DufnaVxel4Pi9X+iwdYpJAC6qNxt1gtaFfGVoa66ypeEkCKXa7WivImnIwFWfhNrMD1hAIojAVRyQHQMpFu7KgmVSM5E+MMARrCCHw25ScxAYtOEmsKvhUpGjSyVS0pA14dPTCZGV5iX54tMLq4hqS9mZnV2A8FXFRhRgZDQ9VlJVO8YLUj0AZl7hFIBrREGo6u0LD6dDvWIv12/305ESVAvgMxi45GQqLoCTCXm/gAmxgA3cR/gffQJr36RFqngAAAABJRU5ErkJggg==) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 78rpx;
}
.group_15 {
  width: 78rpx;
  height: 78rpx;
  display: flex;
  flex-direction: column;
}
.text-group_2 {
  width: 78rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 6rpx 0 0 6rpx;
}
.image-text_4 {
  width: 82rpx;
  height: 116rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 26rpx 26rpx 0 430rpx;
}
.icon_3 {
  width: 78rpx;
  height: 78rpx;
}
.text-group_3 {
  width: 78rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 4rpx;
}
.image_2 {
  position: absolute;
  left: 580rpx;
  top: 154rpx;
  width: 152rpx;
  height: 50rpx;
}
.box_17 {
  background-color: rgba(195,207,221,1.000000);
  height: 536rpx;
  width: 96rpx;
  position: absolute;
  left: 582rpx;
  top: 112rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.box_25 {
  width: 80rpx;
  height: 50rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 8rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 36rpx;
}
.text-wrapper_46 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_60 {
  width: 30rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 2rpx 0 0 12rpx;
}
.box_26 {
  width: 44rpx;
  height: 50rpx;
  margin-left: 44rpx;
  display: flex;
  flex-direction: row;
}
.text-wrapper_47 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_61 {
  width: 28rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 12rpx;
}
.box_27 {
  width: 72rpx;
  height: 148rpx;
  margin-left: 16rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.paragraph_1 {
  width: 16rpx;
  height: 70rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 20rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 28rpx;
  margin-top: 34rpx;
}
.box_28 {
  width: 44rpx;
  height: 148rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_48 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_62 {
  width: 32rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 2rpx 0 0 12rpx;
}
.text-wrapper_49 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-top: -2rpx;
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_63 {
  width: 28rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 12rpx;
}
.text-wrapper_50 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_64 {
  width: 28rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 14rpx;
}
.box_29 {
  width: 44rpx;
  height: 50rpx;
  margin-left: 44rpx;
  display: flex;
  flex-direction: row;
}
.section_2 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.group_21 {
  position: relative;
  width: 44rpx;
  height: 50rpx;
  display: flex;
  flex-direction: row;
}
.text_65 {
  width: 30rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 14rpx;
}
.box_18 {
  position: absolute;
  left: 6rpx;
  top: 4rpx;
  width: 38rpx;
  height: 40rpx;
  display: flex;
  flex-direction: column;
}
.box_30 {
  width: 44rpx;
  height: 50rpx;
  margin-left: 44rpx;
  display: flex;
  flex-direction: row;
}
.text-wrapper_51 {
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_66 {
  width: 28rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 14rpx;
}
.box_31 {
  width: 44rpx;
  height: 50rpx;
  margin-left: 44rpx;
  display: flex;
  flex-direction: row;
}
.text-wrapper_52 {
  background-color: rgba(5,88,42,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_67 {
  width: 30rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 2rpx 0 0 16rpx;
}
.box_32 {
  width: 44rpx;
  height: 100rpx;
  flex-direction: row;
  display: flex;
  margin: -2rpx 0 20rpx 44rpx;
}
.box_19 {
  background-color: rgba(104,102,98,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-top: 50rpx;
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text-wrapper_53 {
  height: 50rpx;
  display: flex;
  flex-direction: column;
  width: 44rpx;
  background-color: rgba(255,255,255,1.000000);
}
.text_68 {
  width: 30rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 2rpx 0 0 12rpx;
}
.text-wrapper_54 {
  background-color: rgba(255,255,255,1.000000);
  height: 50rpx;
  border: 0.5px solid rgba(225,204,177,1);
  margin-left: -44rpx;
  display: flex;
  flex-direction: column;
  width: 44rpx;
}
.text_69 {
  width: 28rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(182,124,130,1.000000);
  font-size: 24rpx;
  font-family: Source Han Sans CN-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 4rpx 0 0 16rpx;
}
.image_3 {
  position: absolute;
  left: 622rpx;
  top: 728rpx;
  width: 152rpx;
  height: 50rpx;
}
.box_20 {
  height: 52rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAAaCAYAAAAwspV7AAAAwElEQVR4nO3WwQnCMBTG8S/Bu3WDjJZ6CHGKOoYgiKO4gSMYJ7AbPA+28FCCeYUkVfqHdylJ+ZFQqIKsZphvGeF7+fog3AsLgDLPRUtVJZKiQg7Ee39xUkVaUKlpANi59lobwtMAQKQ2tSG8+V6fUvSoDWGtX9en0FeG8Jr5Xl8s72zn3ZZKYcaiKO9sB+g9CC17HHKDgAiKgw7H07kEhPeBqg2akkH+n7zb7319tVpQqS2oxMxqwqa7cH0/TPL6JxtxVGuu3NkyAAAAAElFTkSuQmCC) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 74rpx;
  position: absolute;
  left: 60rpx;
  top: 720rpx;
}
.box_21 {
  width: 52rpx;
  height: 52rpx;
  margin-left: 22rpx;
  display: flex;
  flex-direction: column;
}
.thumbnail_4 {
  position: absolute;
  left: -6rpx;
  top: 16rpx;
  width: 20rpx;
  height: 8rpx;
  border: 1.3333333730697632px solid rgba(104,102,98,1);
}
.group_37 {
  width: 750rpx;
  height: 194rpx;
  display: flex;
  flex-direction: column;
}
.block_7 {
  background-color: rgba(255,252,247,1.000000);
  border-radius: 12rpx;
  width: 702rpx;
  height: 96rpx;
  flex-direction: row;
  display: flex;
  margin: 10rpx 0 0 28rpx;
}
.checkbox_1 {
  border-radius: 8rpx;
  height: 32rpx;
  border: 1px solid rgba(0,0,0,1);
  display: flex;
  flex-direction: column;
  width: 32rpx;
  margin: 32rpx 0 0 18rpx;
}
.text_op{
  width: 106rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
  flex: 1;
  position: relative;
  left: 40rpx;
}
.lkop{
  overflow-wrap: break-word;
  color: rgba(104,102,98,1.000000);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  white-space: nowrap;
}
.group_26 {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
}
.group_27 {
  background-color: rgba(94,56,12,1.000000);
  border-radius: 8rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  width: 32rpx;
  margin: 32rpx 0 0 230rpx;
}
.section_3 {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
}
.group_28 {
  background-color: rgba(5,88,42,1.000000);
  border-radius: 8rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  width: 32rpx;
  margin: 30rpx 126rpx 0 232rpx;
}
.group_29 {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
}
.box_33 {
  width: 694rpx;
  height: 76rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 6rpx 0 6rpx 32rpx;
}
.box_22 {
  background-color: rgba(255,252,247,1.000000);
  border-radius: 12rpx;
  width: 172rpx;
  height: 76rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.bnml {
  background-color: rgba(255,252,247,1.000000);
  border-radius: 12rpx;
  width: 510rpx;
  height: 76rpx;
  display: flex;
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(104,102,98,1.000000);
  font-size: 30rpx;
  font-family: Source Han Sans CN-Medium;
  font-weight: 500;
  white-space: nowrap;
}
