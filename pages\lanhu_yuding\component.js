Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 日历相关数据
    showCalendar: false,
    currentDate: new Date(),
    selectedDate: '',
    horizontalDates: [],
    calendarDays: [],
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth(),

    // 座位数据
    seats: {
      // 阳台区座位 1-9
      balcony: [
        { id: 1, number: '1', status: 'available' },
        { id: 2, number: '2', status: 'available' },
        { id: 3, number: '3', status: 'available' },
        { id: 4, number: '4', status: 'available' },
        { id: 5, number: '5', status: 'available' },
        { id: 6, number: '6', status: 'available' },
        { id: 7, number: '7', status: 'available' },
        { id: 8, number: '8', status: 'available' },
        { id: 9, number: '9', status: 'available' }
      ],
      // 高柜区座位 20-47
      highCabinet: [
        { id: 20, number: '20', status: 'available' },
        { id: 21, number: '21', status: 'available' },
        { id: 22, number: '22', status: 'available' },
        { id: 23, number: '23', status: 'available' },
        { id: 24, number: '24', status: 'available' },
        { id: 25, number: '25', status: 'occupied' }, // 示例：已占用状态
        { id: 26, number: '26', status: 'available' },
        { id: 27, number: '27', status: 'available' },
        { id: 28, number: '28', status: 'available' },
        { id: 29, number: '29', status: 'available' },
        { id: 30, number: '30', status: 'available' },
        { id: 31, number: '31', status: 'available' },
        { id: 32, number: '32', status: 'available' },
        { id: 33, number: '33', status: 'available' },
        { id: 34, number: '34', status: 'available' },
        { id: 35, number: '35', status: 'available' },
        { id: 36, number: '36', status: 'available' },
        { id: 37, number: '37', status: 'available' },
        { id: 38, number: '38', status: 'available' },
        { id: 39, number: '39', status: 'available' },
        { id: 40, number: '40', status: 'available' },
        { id: 41, number: '41', status: 'available' },
        { id: 42, number: '42', status: 'available' },
        { id: 43, number: '43', status: 'available' },
        { id: 44, number: '44', status: 'available' },
        { id: 45, number: '45', status: 'available' },
        { id: 46, number: '46', status: 'available' },
        { id: 47, number: '47', status: 'available' }
      ],
      // 沉浸区座位 48-51, 10-19
      immersion: [
        { id: 48, number: '48', status: 'available' },
        { id: 49, number: '49', status: 'available' },
        { id: 50, number: '50', status: 'available' },
        { id: 51, number: '51', status: 'available' },
        { id: 10, number: '10', status: 'available' },
        { id: 11, number: '11', status: 'available' },
        { id: 12, number: '12', status: 'available' },
        { id: 13, number: '13', status: 'available' },
        { id: 14, number: '14', status: 'available' },
        { id: 15, number: '15', status: 'available' },
        { id: 16, number: '16', status: 'available' },
        { id: 17, number: '17', status: 'occupied' }, // 示例：已占用状态
        { id: 18, number: '18', status: 'available' },
        { id: 19, number: '19', status: 'available' }
      ]
    },
    selectedSeats: [] // 已选中的座位ID数组
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("预定页面加载");
    this.initCalendar();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("预定页面渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("预定页面显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("预定页面隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("预定页面卸载");
  },

  /**
   * 点击事件处理函数
   */
  onClick: function () {
    wx.showToast({
      title: '预定功能开发中',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 入座功能
   */
  onSeatEntry: function () {
    const selectedSeats = this.data.selectedSeats;

    // 检查选中座位数量
    if (selectedSeats.length === 0) {
      wx.showToast({
        title: '请先选择座位',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (selectedSeats.length > 1) {
      wx.showToast({
        title: '请选择您个人的座位',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 只有一个座位被选中，执行入座逻辑
    const seatId = selectedSeats[0];
    const seatInfo = this.findSeatById(seatId);

    if (!seatInfo) {
      wx.showToast({
        title: '座位信息错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 更新座位状态为已占用
    seatInfo.seat.status = 'occupied';

    // 清空选中座位列表
    this.setData({
      selectedSeats: [],
      seats: this.data.seats
    });

    wx.showToast({
      title: '入座成功',
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 根据座位ID查找座位信息
   */
  findSeatById: function (seatId) {
    const seatTypes = ['balcony', 'highCabinet', 'immersion'];

    for (let type of seatTypes) {
      const seats = this.data.seats[type];
      const seat = seats.find(s => s.id === seatId);
      if (seat) {
        return { seat, type };
      }
    }

    return null;
  },

  /**
   * 座位点击事件
   */
  onSeatClick: function (e) {
    const seatId = e.currentTarget.dataset.seatId;
    const seatType = e.currentTarget.dataset.seatType;

    // 获取座位信息
    const seat = this.getSeatById(seatId, seatType);
    if (!seat) return;

    // 如果座位已被占用，不允许选择
    if (seat.status === 'occupied') {
      wx.showToast({
        title: '该座位已被占用',
        icon: 'none',
        duration: 1500
      });
      return;
    }

    // 切换座位选中状态
    const selectedSeats = [...this.data.selectedSeats]; // 创建副本避免直接修改
    const index = selectedSeats.indexOf(seatId);

    if (index > -1) {
      // 取消选中
      selectedSeats.splice(index, 1);
      seat.status = 'available';
    } else {
      // 选中座位
      selectedSeats.push(seatId);
      seat.status = 'selected';
    }

    // 更新所有座位类型的数据
    this.setData({
      selectedSeats: selectedSeats,
      seats: this.data.seats
    });
  },

  /**
   * 根据ID和类型获取座位信息
   */
  getSeatById: function (seatId, seatType) {
    const seats = this.data.seats[seatType];
    return seats.find(seat => seat.id === seatId);
  },

  /**
   * 获取座位样式类名
   */
  getSeatClass: function (seat) {
    switch (seat.status) {
      case 'selected':
        return 'seat-selected';
      case 'occupied':
        return 'seat-occupied';
      default:
        return 'seat-available';
    }
  },

  /**
   * 显示日历
   */
  showCalendar: function () {
    this.setData({
      showCalendar: true
    });
  },

  /**
   * 隐藏日历
   */
  hideCalendar: function () {
    this.setData({
      showCalendar: false
    });
  },

  /**
   * 初始化日历
   */
  initCalendar: function () {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth();

    this.setData({
      selectedDate: this.formatDate(now),
      currentYear: year,
      currentMonth: month
    });

    this.generateHorizontalDates();
    this.generateCalendar(year, month);
  },

  /**
   * 生成横向日期数据
   */
  generateHorizontalDates: function () {
    const today = new Date();
    const dates = [];
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const todayFullDate = this.formatDate(today);

    // 生成今天和接下来4天的日期
    for (let i = 0; i < 5; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);

      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const dateStr = `${month}.${day}`;
      const fullDate = this.formatDate(date);

      let weekday;
      if (i === 0) {
        weekday = '今天';
      } else {
        weekday = weekdays[date.getDay()];
      }

      dates.push({
        fullDate: fullDate,
        dateStr: dateStr,
        weekday: weekday,
        isToday: i === 0,
        // 如果没有选中日期，默认选中今天；否则根据选中日期判断
        isSelected: this.data.selectedDate ? (fullDate === this.data.selectedDate) : (i === 0)
      });
    }

    // 如果没有选中日期，设置今天为选中日期
    if (!this.data.selectedDate) {
      this.setData({
        selectedDate: todayFullDate
      });
    }

    this.setData({
      horizontalDates: dates
    });
  },

  /**
   * 生成月历数据
   */
  generateCalendar: function (year, month) {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayWeek = firstDay.getDay();
    const daysInMonth = lastDay.getDate();

    const days = [];

    // 添加上个月的日期填充
    for (let i = 0; i < firstDayWeek; i++) {
      const prevDate = new Date(year, month, -firstDayWeek + i + 1);
      days.push({
        date: prevDate.getDate(),
        fullDate: this.formatDate(prevDate),
        isCurrentMonth: false,
        isToday: false,
        isSelected: false
      });
    }

    // 添加当月日期
    const today = new Date();
    for (let i = 1; i <= daysInMonth; i++) {
      const currentDate = new Date(year, month, i);
      const fullDate = this.formatDate(currentDate);
      days.push({
        date: i,
        fullDate: fullDate,
        isCurrentMonth: true,
        isToday: this.isSameDay(currentDate, today),
        isSelected: fullDate === this.data.selectedDate
      });
    }

    // 添加下个月的日期填充
    const remainingDays = 42 - days.length; // 6行 * 7天
    for (let i = 1; i <= remainingDays; i++) {
      const nextDate = new Date(year, month + 1, i);
      days.push({
        date: i,
        fullDate: this.formatDate(nextDate),
        isCurrentMonth: false,
        isToday: false,
        isSelected: false
      });
    }

    this.setData({
      calendarDays: days
    });
  },



  /**
   * 选择横向日期（弹窗日历）
   */
  selectHorizontalDate: function (e) {
    const fullDate = e.currentTarget.dataset.date;

    // 更新选中日期 - 确保只有被点击的日期被选中
    const dates = this.data.horizontalDates.map(date => ({
      ...date,
      isSelected: date.fullDate === fullDate
    }));

    this.setData({
      selectedDate: fullDate,
      horizontalDates: dates
    });

    // 关闭日历
    this.hideCalendar();

    // 显示选择反馈
    const selectedDate = dates.find(date => date.fullDate === fullDate);
    const displayText = selectedDate ? `${selectedDate.weekday} ${selectedDate.dateStr}` : fullDate;

    wx.showToast({
      title: `已选择 ${displayText}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 选择顶部日期
   */
  selectTopDate: function (e) {
    const fullDate = e.currentTarget.dataset.date;

    // 更新选中日期 - 确保只有被点击的日期被选中
    const dates = this.data.horizontalDates.map(date => ({
      ...date,
      isSelected: date.fullDate === fullDate
    }));

    this.setData({
      selectedDate: fullDate,
      horizontalDates: dates
    });

    // 显示选择反馈
    const selectedDate = dates.find(date => date.fullDate === fullDate);
    const displayText = selectedDate ? `${selectedDate.weekday} ${selectedDate.dateStr}` : fullDate;

    wx.showToast({
      title: `已选择 ${displayText}`,
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 上一月
   */
  prevMonth: function () {
    let year = this.data.currentYear;
    let month = this.data.currentMonth - 1;

    if (month < 0) {
      month = 11;
      year--;
    }

    this.setData({
      currentYear: year,
      currentMonth: month
    });

    this.generateCalendar(year, month);
  },

  /**
   * 下一月
   */
  nextMonth: function () {
    let year = this.data.currentYear;
    let month = this.data.currentMonth + 1;

    if (month > 11) {
      month = 0;
      year++;
    }

    this.setData({
      currentYear: year,
      currentMonth: month
    });

    this.generateCalendar(year, month);
  },

  /**
   * 选择日期（月历）
   */
  selectDate: function (e) {
    const fullDate = e.currentTarget.dataset.date;
    const isCurrentMonth = e.currentTarget.dataset.currentMonth;

    if (!isCurrentMonth) return;

    // 更新选中日期
    const days = this.data.calendarDays.map(day => ({
      ...day,
      isSelected: day.fullDate === fullDate
    }));

    // 同时更新横向日期数据
    const horizontalDates = this.data.horizontalDates.map(date => ({
      ...date,
      isSelected: date.fullDate === fullDate
    }));

    this.setData({
      selectedDate: fullDate,
      calendarDays: days,
      horizontalDates: horizontalDates
    });

    // 关闭日历
    this.hideCalendar();

    wx.showToast({
      title: `已选择 ${fullDate}`,
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 格式化日期
   */
  formatDate: function (date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 判断是否为同一天
   */
  isSameDay: function (date1, date2) {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  },

  /**
   * 获取月份名称
   */
  getMonthName: function (month) {
    const months = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    return months[month];
  }
});
