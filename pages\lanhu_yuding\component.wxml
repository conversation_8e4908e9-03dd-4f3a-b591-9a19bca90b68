<view class="page">
  <view class="block_1">
    <view class="group_31">
      <text class="text_1">即刻预定</text>
      <view class="group_2">
        <view class="section_1 {{item.isSelected ? 'date-selected' : ''}}"
              wx:for="{{horizontalDates}}"
              wx:key="fullDate"
              bindtap="selectTopDate"
              data-date="{{item.fullDate}}">
          <view class="text-group_4">
            <text class="text_2 {{item.isSelected ? 'text-selected' : ''}}">{{item.weekday}}</text>
            <text class="text_3 {{item.isSelected ? 'text-selected' : ''}}">{{item.dateStr}}</text>
          </view>
        </view>
        <image class="block_3" src="/images/lanhu_yuding/签到.png" bindtap="showCalendar"></image>
      </view>
    </view>
    <view class="group_5">
      <view class="box_1">
        <view class="group_32">
          <view class="box_3 {{seats.balcony[0].status === 'selected' ? 'seat-selected' : seats.balcony[0].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.balcony[0].id}}"
                data-seat-type="balcony">
            <view class="text-wrapper_1">
              <text class="text_8">{{seats.balcony[0].number}}</text>
            </view>
          </view>
          <view class="block_10">
            <view class="text-wrapper_2 {{seats.balcony[1].status === 'selected' ? 'seat-selected' : seats.balcony[1].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[1].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[1].number}}</text>
            </view>
            <view class="text-wrapper_3 {{seats.balcony[2].status === 'selected' ? 'seat-selected' : seats.balcony[2].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[2].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[2].number}}</text>
            </view>
            <view class="text-wrapper_4 {{seats.balcony[3].status === 'selected' ? 'seat-selected' : seats.balcony[3].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[3].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[3].number}}</text>
            </view>
            <view class="text-wrapper_5 {{seats.balcony[4].status === 'selected' ? 'seat-selected' : seats.balcony[4].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[4].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[4].number}}</text>
            </view>
            <view class="text-wrapper_6 {{seats.balcony[5].status === 'selected' ? 'seat-selected' : seats.balcony[5].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[5].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[5].number}}</text>
            </view>
            <view class="text-wrapper_7 {{seats.balcony[6].status === 'selected' ? 'seat-selected' : seats.balcony[6].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[6].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[6].number}}</text>
            </view>
            <view class="text-wrapper_8 {{seats.balcony[7].status === 'selected' ? 'seat-selected' : seats.balcony[7].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[7].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[7].number}}</text>
            </view>
            <view class="text-wrapper_9 {{seats.balcony[8].status === 'selected' ? 'seat-selected' : seats.balcony[8].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.balcony[8].id}}"
                  data-seat-type="balcony">
              <text class="text_8">{{seats.balcony[8].number}}</text>
            </view>
          </view>
        </view>
        <view class="group_33">
          <text class="text_17">阳台区</text>
        </view>
      </view>
      <view class="box_23">
        <image src="../../images/lanhu_yuding/alop.png" class="corridor_left_image"></image>
        <text class="text_18">公共通道</text>
        <image src="../../images/lanhu_yuding/alpo.png" class="corridor_right_image"></image>
      </view>
      <view class="box_11">
        <view class="group_34">
          <view class="block_4 {{seats.highCabinet[0].status === 'selected' ? 'seat-selected' : seats.highCabinet[0].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[0].id}}"
                data-seat-type="highCabinet">
            <text class="text_19">{{seats.highCabinet[0].number}}</text>
            <view class="box_12"></view>
          </view>
          <view class="text-wrapper_10 {{seats.highCabinet[1].status === 'selected' ? 'seat-selected' : seats.highCabinet[1].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[1].id}}"
                data-seat-type="highCabinet">
            <text class="text_20">{{seats.highCabinet[1].number}}</text>
          </view>
          <view class="text-wrapper_11 {{seats.highCabinet[2].status === 'selected' ? 'seat-selected' : seats.highCabinet[2].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[2].id}}"
                data-seat-type="highCabinet">
            <text class="text_21">{{seats.highCabinet[2].number}}</text>
          </view>
          <view class="text-wrapper_12 {{seats.highCabinet[3].status === 'selected' ? 'seat-selected' : seats.highCabinet[3].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[3].id}}"
                data-seat-type="highCabinet">
            <text class="text_22">{{seats.highCabinet[3].number}}</text>
          </view>
          <view class="text-wrapper_13 {{seats.highCabinet[4].status === 'selected' ? 'seat-selected' : seats.highCabinet[4].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[4].id}}"
                data-seat-type="highCabinet">
            <text class="text_23">{{seats.highCabinet[4].number}}</text>
          </view>
          <view class="text-wrapper_14 {{seats.highCabinet[5].status === 'selected' ? 'seat-selected' : seats.highCabinet[5].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[5].id}}"
                data-seat-type="highCabinet">
            <text class="text_24">{{seats.highCabinet[5].number}}</text>
          </view>
          <view class="text-wrapper_15 {{seats.highCabinet[6].status === 'selected' ? 'seat-selected' : seats.highCabinet[6].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[6].id}}"
                data-seat-type="highCabinet">
            <text class="text_25">{{seats.highCabinet[6].number}}</text>
          </view>
        </view>
        <view class="group_35">
          <view class="text-wrapper_16 {{seats.highCabinet[7].status === 'selected' ? 'seat-selected' : seats.highCabinet[7].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[7].id}}"
                data-seat-type="highCabinet">
            <text class="text_26">{{seats.highCabinet[7].number}}</text>
          </view>
          <view class="text-wrapper_17 {{seats.highCabinet[8].status === 'selected' ? 'seat-selected' : seats.highCabinet[8].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[8].id}}"
                data-seat-type="highCabinet">
            <text class="text_27">{{seats.highCabinet[8].number}}</text>
          </view>
          <view class="text-wrapper_18 {{seats.highCabinet[9].status === 'selected' ? 'seat-selected' : seats.highCabinet[9].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[9].id}}"
                data-seat-type="highCabinet">
            <text class="text_28">{{seats.highCabinet[9].number}}</text>
          </view>
          <view class="text-wrapper_19 {{seats.highCabinet[10].status === 'selected' ? 'seat-selected' : seats.highCabinet[10].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[10].id}}"
                data-seat-type="highCabinet">
            <text class="text_29">{{seats.highCabinet[10].number}}</text>
          </view>
          <view class="text-wrapper_20 {{seats.highCabinet[11].status === 'selected' ? 'seat-selected' : seats.highCabinet[11].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[11].id}}"
                data-seat-type="highCabinet">
            <text class="text_30">{{seats.highCabinet[11].number}}</text>
          </view>
          <view class="text-wrapper_21 {{seats.highCabinet[12].status === 'selected' ? 'seat-selected' : seats.highCabinet[12].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[12].id}}"
                data-seat-type="highCabinet">
            <text class="text_31">{{seats.highCabinet[12].number}}</text>
          </view>
          <view class="text-wrapper_22 {{seats.highCabinet[13].status === 'selected' ? 'seat-selected' : seats.highCabinet[13].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.highCabinet[13].id}}"
                data-seat-type="highCabinet">
            <text class="text_32">{{seats.highCabinet[13].number}}</text>
          </view>
        </view>
        <view class="text-wrapper_55">
          <text class="text_33">高柜区</text>
        </view>
        <view class="group_10">
          <view class="grid_2">
            <view class="text-wrapper_24 {{seats.highCabinet[14].status === 'selected' ? 'seat-selected' : seats.highCabinet[14].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[14].id}}"
                  data-seat-type="highCabinet">
              <text class="text_34">{{seats.highCabinet[14].number}}</text>
            </view>
            <view class="text-wrapper_25 {{seats.highCabinet[15].status === 'selected' ? 'seat-selected' : seats.highCabinet[15].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[15].id}}"
                  data-seat-type="highCabinet">
              <text class="text_35">{{seats.highCabinet[15].number}}</text>
            </view>
            <view class="text-wrapper_26 {{seats.highCabinet[16].status === 'selected' ? 'seat-selected' : seats.highCabinet[16].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[16].id}}"
                  data-seat-type="highCabinet">
              <text class="text_36">{{seats.highCabinet[16].number}}</text>
            </view>
            <view class="text-wrapper_27 {{seats.highCabinet[17].status === 'selected' ? 'seat-selected' : seats.highCabinet[17].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[17].id}}"
                  data-seat-type="highCabinet">
              <text class="text_37">{{seats.highCabinet[17].number}}</text>
            </view>
            <view class="text-wrapper_28 {{seats.highCabinet[18].status === 'selected' ? 'seat-selected' : seats.highCabinet[18].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[18].id}}"
                  data-seat-type="highCabinet">
              <text class="text_38">{{seats.highCabinet[18].number}}</text>
            </view>
            <view class="text-wrapper_29 {{seats.highCabinet[19].status === 'selected' ? 'seat-selected' : seats.highCabinet[19].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[19].id}}"
                  data-seat-type="highCabinet">
              <text class="text_39">{{seats.highCabinet[19].number}}</text>
            </view>
            <view class="text-wrapper_30 {{seats.highCabinet[20].status === 'selected' ? 'seat-selected' : seats.highCabinet[20].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[20].id}}"
                  data-seat-type="highCabinet">
              <text class="text_40">{{seats.highCabinet[20].number}}</text>
            </view>
            <view class="text-wrapper_31 {{seats.highCabinet[21].status === 'selected' ? 'seat-selected' : seats.highCabinet[21].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[21].id}}"
                  data-seat-type="highCabinet">
              <text class="text_41">{{seats.highCabinet[21].number}}</text>
            </view>
            <view class="text-wrapper_32 {{seats.highCabinet[22].status === 'selected' ? 'seat-selected' : seats.highCabinet[22].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[22].id}}"
                  data-seat-type="highCabinet">
              <text class="text_42">{{seats.highCabinet[22].number}}</text>
            </view>
            <view class="text-wrapper_33 {{seats.highCabinet[23].status === 'selected' ? 'seat-selected' : seats.highCabinet[23].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[23].id}}"
                  data-seat-type="highCabinet">
              <text class="text_43">{{seats.highCabinet[23].number}}</text>
            </view>
            <view class="text-wrapper_34 {{seats.highCabinet[24].status === 'selected' ? 'seat-selected' : seats.highCabinet[24].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[24].id}}"
                  data-seat-type="highCabinet">
              <text class="text_44">{{seats.highCabinet[24].number}}</text>
            </view>
            <view class="text-wrapper_35 {{seats.highCabinet[25].status === 'selected' ? 'seat-selected' : seats.highCabinet[25].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[25].id}}"
                  data-seat-type="highCabinet">
              <text class="text_45">{{seats.highCabinet[25].number}}</text>
            </view>
            <view class="text-wrapper_36 {{seats.highCabinet[26].status === 'selected' ? 'seat-selected' : seats.highCabinet[26].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[26].id}}"
                  data-seat-type="highCabinet">
              <text class="text_46">{{seats.highCabinet[26].number}}</text>
            </view>
            <view class="text-wrapper_37 {{seats.highCabinet[27].status === 'selected' ? 'seat-selected' : seats.highCabinet[27].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.highCabinet[27].id}}"
                  data-seat-type="highCabinet">
              <text class="text_47">{{seats.highCabinet[27].number}}</text>
            </view>
          </view>
          <image src="../../images/lanhu_yuding/FigmaDDSSlicePNG8328d3c1fe8a4b33e8da8a27f995499e.png" class="thumbnail_1"></image>
        </view>
      </view>
      <view class="box_24">
        <view class="group_11">
          <view class="block_5">
            <view class="section_4">
              <view class="group_36">
                <view class="text-wrapper_38 {{seats.immersion[0].status === 'selected' ? 'seat-selected' : seats.immersion[0].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                      bindtap="onSeatClick"
                      data-seat-id="{{seats.immersion[0].id}}"
                      data-seat-type="immersion">
                  <text class="text_48">{{seats.immersion[0].number}}</text>
                </view>
                <view class="text-wrapper_39 {{seats.immersion[1].status === 'selected' ? 'seat-selected' : seats.immersion[1].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                      bindtap="onSeatClick"
                      data-seat-id="{{seats.immersion[1].id}}"
                      data-seat-type="immersion">
                  <text class="text_49">{{seats.immersion[1].number}}</text>
                </view>
                <view class="text-wrapper_40 {{seats.immersion[2].status === 'selected' ? 'seat-selected' : seats.immersion[2].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                      bindtap="onSeatClick"
                      data-seat-id="{{seats.immersion[2].id}}"
                      data-seat-type="immersion">
                  <text class="text_50">{{seats.immersion[2].number}}</text>
                </view>
                <view class="text-wrapper_41 {{seats.immersion[3].status === 'selected' ? 'seat-selected' : seats.immersion[3].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                      bindtap="onSeatClick"
                      data-seat-id="{{seats.immersion[3].id}}"
                      data-seat-type="immersion">
                  <text class="text_51">{{seats.immersion[3].number}}</text>
                </view>
              </view>
              <view class="text-wrapper_42">
                <text class="text_52">柜子</text>
                <text class="text_53">1</text>
              </view>
              <view class="text-wrapper_43">
                <text class="text_54">柜子</text>
                <text class="text_55">2</text>
              </view>
              <view class="text-wrapper_44">
                <text class="text_56">柜子</text>
                <text class="text_57">3</text>
              </view>
            </view>
            <view class="text-wrapper_56">
              <text class="text_58">沉浸区</text>
            </view>
            <image src="../../images/lanhu_yuding/FigmaDDSSlicePNG3707bf871a1f08a224aefc40cbeb9dcb.png" class="thumbnail_2"></image>
          </view>
        </view>
        <view class="group_13"></view>
      </view>
      <view class="box_14">
        <view class="box_15">
          <image src="../../images/lanhu_yuding/alop.png" class="corridor_left_image"></image>
          <text class="text_59">公共通道</text>
          <image src="../../images/lanhu_yuding/alpo.png" class="corridor_right_image"></image>
        </view>
      </view>
      <view class="box_16">
        <view class="group_14">
          <view class="image-text_3">
            <view class="icon_2">
              <view class="group_15"></view>
            </view>
            <text class="text-group_2">背诵区</text>
          </view>
          <view class="image-text_4">
            <image src="../../images/lanhu_yuding/FigmaDDSSlicePNG0600053cf9b4499d8977bf7330aba69d.png" class="icon_3"></image>
            <text class="text-group_3">背诵区</text>
          </view>
        </view>
      </view>
      
      <view class="box_17">
        <view class="box_25">
          <image src="../../images/lanhu_yuding/FigmaDDSSlicePNG9a55e14cefd5baafe52f8c1717f56a8c.png" class="thumbnail_3"></image>
          <view class="text-wrapper_46 {{seats.immersion[4].status === 'selected' ? 'seat-selected' : seats.immersion[4].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[4].id}}"
                data-seat-type="immersion">
            <text class="text_60">{{seats.immersion[4].number}}</text>
          </view>
        </view>
        <view class="box_26">
          <view class="text-wrapper_47 {{seats.immersion[5].status === 'selected' ? 'seat-selected' : seats.immersion[5].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[5].id}}"
                data-seat-type="immersion">
            <text class="text_61">{{seats.immersion[5].number}}</text>
          </view>
        </view>
        <view class="box_27">
          <text class="paragraph_1">沉\n浸\n区</text>
          <view class="box_28">
            <view class="text-wrapper_48 {{seats.immersion[6].status === 'selected' ? 'seat-selected' : seats.immersion[6].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.immersion[6].id}}"
                  data-seat-type="immersion">
              <text class="text_62">{{seats.immersion[6].number}}</text>
            </view>
            <view class="text-wrapper_49 {{seats.immersion[7].status === 'selected' ? 'seat-selected' : seats.immersion[7].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.immersion[7].id}}"
                  data-seat-type="immersion">
              <text class="text_63">{{seats.immersion[7].number}}</text>
            </view>
            <view class="text-wrapper_50 {{seats.immersion[8].status === 'selected' ? 'seat-selected' : seats.immersion[8].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.immersion[8].id}}"
                  data-seat-type="immersion">
              <text class="text_64">{{seats.immersion[8].number}}</text>
            </view>
          </view>
        </view>
        <view class="box_29">
          <view class="section_2 {{seats.immersion[9].status === 'selected' ? 'seat-selected' : seats.immersion[9].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[9].id}}"
                data-seat-type="immersion">
            <view class="group_21">
              <text class="text_65">{{seats.immersion[9].number}}</text>
            </view>
          </view>
        </view>
        <view class="box_30">
          <view class="section_2 {{seats.immersion[10].status === 'selected' ? 'seat-selected' : seats.immersion[10].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[10].id}}"
                data-seat-type="immersion">
            <text class="text_66">{{seats.immersion[10].number}}</text>
          </view>
        </view>
        <view class="box_31">
          <view class="section_2 {{seats.immersion[11].status === 'selected' ? 'seat-selected' : seats.immersion[11].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[11].id}}"
                data-seat-type="immersion">
            <text class="text_67">{{seats.immersion[11].number}}</text>
          </view>
        </view>
        <view class="box_32">
          <view class="box_19">
            <view class="text-wrapper_53 {{seats.immersion[13].status === 'selected' ? 'seat-selected' : seats.immersion[13].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                  bindtap="onSeatClick"
                  data-seat-id="{{seats.immersion[13].id}}"
                  data-seat-type="immersion">
              <text class="text_68">{{seats.immersion[13].number}}</text>
            </view>
          </view>
          <view class="text-wrapper_54 {{seats.immersion[12].status === 'selected' ? 'seat-selected' : seats.immersion[12].status === 'occupied' ? 'seat-occupied' : 'seat-available'}}"
                bindtap="onSeatClick"
                data-seat-id="{{seats.immersion[12].id}}"
                data-seat-type="immersion">
            <text class="text_69">{{seats.immersion[12].number}}</text>
          </view>
        </view>
      </view>
     
    </view>
    <view class="group_37">
      <view class="block_7">
        <view class="checkbox_1">
    <text class="text_op">可用位置</text>
        </view>
        <view class="group_27">
    <text class="text_op">选中位置</text>
        </view>
        <view class="group_28">
    <text class="text_op">已被占用</text>
        </view>
      </view>
      <view class="box_33">
        <view class="box_22" bindtap="onSeatEntry"><text class="lkop">入座</text></view>
        <view class="bnml" bindtap="onClick">预约</view>
      </view>
    </view>

  </view>

  <!-- 日历弹窗 -->
  <view class="calendar-overlay" wx:if="{{showCalendar}}" bindtap="hideCalendar">
    <view class="calendar-popup" catchtap="">
      <!-- 日历头部 -->
      <view class="calendar-header">
        <view class="calendar-nav" bindtap="prevMonth">‹</view>
        <view class="calendar-title">{{currentYear}}年{{currentMonth + 1}}月</view>
        <view class="calendar-nav" bindtap="nextMonth">›</view>
      </view>

      <!-- 星期标题 -->
      <view class="calendar-weekdays">
        <view class="weekday">日</view>
        <view class="weekday">一</view>
        <view class="weekday">二</view>
        <view class="weekday">三</view>
        <view class="weekday">四</view>
        <view class="weekday">五</view>
        <view class="weekday">六</view>
      </view>

      <!-- 日期网格 -->
      <view class="calendar-days">
        <view class="calendar-day {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}}"
              wx:for="{{calendarDays}}"
              wx:key="fullDate"
              bindtap="selectDate"
              data-date="{{item.fullDate}}"
              data-current-month="{{item.isCurrentMonth}}">
          {{item.date}}
        </view>
      </view>

      <!-- 关闭按钮 -->
      <view class="calendar-footer">
        <view class="calendar-close" bindtap="hideCalendar">关闭</view>
      </view>
    </view>
  </view>
</view>