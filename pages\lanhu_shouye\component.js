Page({
  /**
   * 页面的初始数据
   */
  data: {
    phoneNumber: '************', // 自习室联系电话
    address: '浙江省杭州市西湖区西湖街道西湖小区西湖xx', // 默认地址
    currentLocation: '正在获取位置...', // 当前用户位置
    latitude: 30.2741, // 杭州西湖区纬度
    longitude: 120.1551, // 杭州西湖区经度
    locationLoading: true // 位置加载状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("首页加载");
    // 获取用户位置
    this.getCurrentLocation();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("首页渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("首页显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("首页隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("首页卸载");
  },

  /**
   * 一键拨打电话事件处理函数
   */
  onCallPhone: function(e) {
    const phoneNumber = this.data.phoneNumber;

    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${phoneNumber} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phoneNumber,
            success: () => {
              console.log('拨打电话成功');
            },
            fail: (err) => {
              console.error('拨打电话失败:', err);
              wx.showToast({
                title: '拨打失败',
                icon: 'error',
                duration: 2000
              });
            }
          });
        }
      }
    });
  },

  /**
   * 一键查看地图事件处理函数
   */
  onViewMap: function(e) {
    const { latitude, longitude, address } = this.data;

    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: '全部都能考得上自习室',
      address: address,
      scale: 18,
      success: () => {
        console.log('打开地图成功');
      },
      fail: (err) => {
        console.error('打开地图失败:', err);
        wx.showToast({
          title: '打开地图失败',
          icon: 'error',
          duration: 2000
        });
      }
    });
  },

  /**
   * 获取当前位置
   */
  getCurrentLocation: function() {
    console.log('开始获取用户位置');

    // 先检查位置权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，直接获取位置
          this.getLocation();
        } else {
          // 未授权，请求授权
          this.requestLocationPermission();
        }
      },
      fail: (err) => {
        console.error('获取设置失败:', err);
        this.setDefaultLocation();
      }
    });
  },

  /**
   * 请求位置权限
   */
  requestLocationPermission: function() {
    wx.authorize({
      scope: 'scope.userLocation',
      success: () => {
        console.log('位置权限授权成功');
        this.getLocation();
      },
      fail: () => {
        console.log('用户拒绝位置权限');
        // 显示引导用户手动开启权限的提示
        wx.showModal({
          title: '位置权限',
          content: '需要获取您的位置信息来显示当前位置，请在设置中开启位置权限',
          showCancel: true,
          cancelText: '暂不开启',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  if (settingRes.authSetting['scope.userLocation']) {
                    this.getLocation();
                  } else {
                    this.setDefaultLocation();
                  }
                }
              });
            } else {
              this.setDefaultLocation();
            }
          }
        });
      }
    });
  },

  /**
   * 获取位置信息
   */
  getLocation: function() {
    wx.getLocation({
      type: 'gcj02', // 返回可以用于wx.openLocation的坐标
      success: (res) => {
        console.log('获取位置成功:', res);
        const { latitude, longitude } = res;

        // 调用逆地理编码获取地址
        this.reverseGeocode(latitude, longitude);
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        this.setDefaultLocation();

        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 逆地理编码获取地址
   */
  reverseGeocode: function(latitude, longitude) {
    // 这里使用腾讯地图API进行逆地理编码
    // 注意：实际使用时需要申请腾讯地图API密钥
    const key = 'YOUR_TENCENT_MAP_KEY'; // 需要替换为实际的API密钥

    // 模拟API调用，实际项目中应该通过后端调用
    // 这里先使用默认位置，并添加获取成功的标识
    this.setData({
      currentLocation: '当前位置获取成功',
      locationLoading: false
    });

    // 实际的API调用代码（需要后端支持）：
    /*
    wx.request({
      url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}`,
      method: 'GET',
      success: (res) => {
        if (res.data.status === 0) {
          const address = res.data.result.formatted_addresses.recommend;
          this.setData({
            currentLocation: address,
            locationLoading: false
          });
        } else {
          this.setDefaultLocation();
        }
      },
      fail: (err) => {
        console.error('逆地理编码失败:', err);
        this.setDefaultLocation();
      }
    });
    */
  },

  /**
   * 设置默认位置
   */
  setDefaultLocation: function() {
    this.setData({
      currentLocation: this.data.address,
      locationLoading: false
    });
  }
});
