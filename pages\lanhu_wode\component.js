Page({
  /**
   * 页面的初始数据
   */
  data: {
    memberInfo: {
      level: '月卡会员',
      description: '开通会员，享受更多权益'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("我的页面加载");
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("我的页面渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("我的页面显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("我的页面隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("我的页面卸载");
  },

  /**
   * 跳转到订单记录页面
   */
  goToOrders: function() {
    console.log('点击订单记录');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.navigateTo({
        url: '/pages/orders/orders',
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 500);
  },

  /**
   * 显示会员查询界面
   */
  showMemberQuery: function() {
    console.log('点击会员查询');
    const memberInfo = this.data.memberInfo;

    wx.showModal({
      title: '会员信息',
      content: `当前等级：${memberInfo.level}\n${memberInfo.description}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '续费',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到续费页面',
            icon: 'success',
            duration: 2000
          });
          // 这里可以添加跳转到续费页面的逻辑
        }
      }
    });
  },

  /**
   * 跳转到排行查询页面
   */
  goToRanking: function() {
    console.log('点击排行查询');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    setTimeout(() => {
      wx.hideLoading();
      wx.navigateTo({
        url: '/pages/lanhu_paihangbang/component',
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 500);
  },

  /**
   * 显示WiFi连接功能界面
   */
  showWifiConnection: function() {
    console.log('点击WiFi链接');

    wx.showActionSheet({
      itemList: ['查看当前WiFi', '连接新WiFi', 'WiFi设置'],
      success: (res) => {
        const tapIndex = res.tapIndex;
        switch(tapIndex) {
          case 0:
            this.showCurrentWifi();
            break;
          case 1:
            this.connectNewWifi();
            break;
          case 2:
            this.openWifiSettings();
            break;
        }
      },
      fail: (res) => {
        console.log('用户取消选择');
      }
    });
  },

  /**
   * 显示当前WiFi信息
   */
  showCurrentWifi: function() {
    wx.getConnectedWifi({
      success: (res) => {
        wx.showModal({
          title: '当前WiFi',
          content: `WiFi名称：${res.wifi.SSID}\n信号强度：${res.wifi.signalStrength}`,
          showCancel: false
        });
      },
      fail: (err) => {
        wx.showToast({
          title: '获取WiFi信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 连接新WiFi
   */
  connectNewWifi: function() {
    wx.showToast({
      title: '正在搜索WiFi...',
      icon: 'loading',
      duration: 2000
    });

    // 模拟WiFi搜索
    setTimeout(() => {
      wx.showModal({
        title: '可用WiFi',
        content: '自习室WiFi\n图书馆WiFi\n咖啡厅WiFi',
        showCancel: true,
        cancelText: '取消',
        confirmText: '连接',
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '连接成功',
              icon: 'success',
              duration: 2000
            });
          }
        }
      });
    }, 2000);
  },

  /**
   * 打开WiFi设置
   */
  openWifiSettings: function() {
    wx.showToast({
      title: '跳转到系统WiFi设置',
      icon: 'success',
      duration: 2000
    });
  }
});
