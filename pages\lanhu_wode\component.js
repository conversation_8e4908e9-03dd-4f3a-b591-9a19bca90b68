Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      userName: '茉莉雨ov',
      isLoggedIn: false,
      avatarUrl: '',
      nickName: ''
    },
    memberInfo: {
      level: '月卡会员',
      description: '开通会员，享受更多权益'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("我的页面加载");
    console.log("功能函数已加载:", {
      goToOrders: typeof this.goToOrders,
      showMemberQuery: typeof this.showMemberQuery,
      goToRanking: typeof this.goToRanking,
      showWifiConnection: typeof this.showWifiConnection
    });

    // 检查用户登录状态
    this.checkUserLoginStatus();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("我的页面渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("我的页面显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("我的页面隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("我的页面卸载");
  },

  /**
   * 跳转到订单记录页面
   */
  goToOrders: function() {
    console.log('点击订单记录');
    wx.showToast({
      title: '正在跳转到订单页面...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/orders/orders',
        success: () => {
          console.log('跳转成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 1000);
  },

  /**
   * 显示会员查询界面
   */
  showMemberQuery: function() {
    console.log('点击会员查询');
    wx.showModal({
      title: '会员信息',
      content: '当前等级：月卡会员\n开通会员，享受更多权益',
      showCancel: true,
      cancelText: '关闭',
      confirmText: '续费',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到续费页面',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 跳转到排行查询页面
   */
  goToRanking: function() {
    console.log('点击排行查询');
    wx.showToast({
      title: '正在跳转到排行榜...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/lanhu_paihangbang/component',
        success: () => {
          console.log('跳转排行榜成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 1000);
  },

  /**
   * 显示WiFi连接功能界面
   */
  showWifiConnection: function() {
    console.log('点击WiFi链接');

    wx.showActionSheet({
      itemList: ['查看当前WiFi', '连接新WiFi', 'WiFi设置'],
      success: (res) => {
        const tapIndex = res.tapIndex;
        switch(tapIndex) {
          case 0:
            wx.showModal({
              title: '当前WiFi',
              content: 'WiFi名称：自习室WiFi\n信号强度：强',
              showCancel: false
            });
            break;
          case 1:
            wx.showToast({
              title: '正在搜索WiFi...',
              icon: 'loading',
              duration: 2000
            });
            break;
          case 2:
            wx.showToast({
              title: '跳转到WiFi设置',
              icon: 'success',
              duration: 2000
            });
            break;
        }
      },
      fail: (res) => {
        console.log('用户取消选择');
      }
    });
  },

  /**
   * 检查用户登录状态
   */
  checkUserLoginStatus: function() {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.nickName) {
      this.setData({
        'userInfo.userName': userInfo.nickName,
        'userInfo.isLoggedIn': true,
        'userInfo.avatarUrl': userInfo.avatarUrl || '',
        'userInfo.nickName': userInfo.nickName
      });
    } else {
      this.setData({
        'userInfo.userName': '茉莉雨ov',
        'userInfo.isLoggedIn': false
      });
    }
  },

  /**
   * 处理用户登录
   */
  handleUserLogin: function() {
    console.log('点击用户名，准备登录');

    if (this.data.userInfo.isLoggedIn) {
      // 已登录，显示用户信息
      this.showUserProfile();
    } else {
      // 未登录，执行登录流程
      this.performUserLogin();
    }
  },

  /**
   * 显示用户信息
   */
  showUserProfile: function() {
    const userInfo = this.data.userInfo;
    wx.showActionSheet({
      itemList: ['查看个人信息', '退出登录'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 查看个人信息
          wx.showModal({
            title: '个人信息',
            content: `昵称：${userInfo.nickName}\n会员等级：${this.data.memberInfo.level}`,
            showCancel: false,
            confirmText: '确定'
          });
        } else if (res.tapIndex === 1) {
          // 退出登录
          this.performUserLogout();
        }
      }
    });
  },

  /**
   * 执行用户登录
   */
  performUserLogin: function() {
    wx.showLoading({
      title: '登录中...',
      mask: true
    });

    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res);

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', res.userInfo);

        // 更新页面数据
        this.setData({
          'userInfo.userName': res.userInfo.nickName,
          'userInfo.isLoggedIn': true,
          'userInfo.avatarUrl': res.userInfo.avatarUrl,
          'userInfo.nickName': res.userInfo.nickName
        });

        wx.hideLoading();
        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 2000
        });

        // 可以在这里调用后端API进行登录验证
        this.loginToServer(res.userInfo);
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        wx.hideLoading();

        if (err.errMsg.includes('deny')) {
          wx.showModal({
            title: '提示',
            content: '需要您的授权才能获取用户信息，请重新尝试',
            showCancel: false,
            confirmText: '确定'
          });
        } else {
          wx.showToast({
            title: '登录失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 向服务器发送登录请求
   */
  loginToServer: function(userInfo) {
    // 获取登录凭证
    wx.login({
      success: (res) => {
        if (res.code) {
          console.log('登录凭证:', res.code);

          // 这里可以调用后端API
          // wx.request({
          //   url: 'https://your-api.com/login',
          //   method: 'POST',
          //   data: {
          //     code: res.code,
          //     userInfo: userInfo
          //   },
          //   success: (apiRes) => {
          //     console.log('服务器登录成功:', apiRes);
          //   },
          //   fail: (apiErr) => {
          //     console.error('服务器登录失败:', apiErr);
          //   }
          // });

          // 模拟服务器响应
          console.log('模拟服务器登录成功');
        } else {
          console.error('获取登录凭证失败:', res.errMsg);
        }
      },
      fail: (err) => {
        console.error('wx.login失败:', err);
      }
    });
  },

  /**
   * 执行用户退出登录
   */
  performUserLogout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('userInfo');

          // 重置页面数据
          this.setData({
            'userInfo.userName': '茉莉雨ov',
            'userInfo.isLoggedIn': false,
            'userInfo.avatarUrl': '',
            'userInfo.nickName': ''
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  }
});
