Page({
  /**
   * 页面的初始数据
   */
  data: {
    memberInfo: {
      level: '月卡会员',
      description: '开通会员，享受更多权益'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("我的页面加载");
    console.log("功能函数已加载:", {
      goToOrders: typeof this.goToOrders,
      showMemberQuery: typeof this.showMemberQuery,
      goToRanking: typeof this.goToRanking,
      showWifiConnection: typeof this.showWifiConnection
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("我的页面渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("我的页面显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("我的页面隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("我的页面卸载");
  },

  /**
   * 跳转到订单记录页面
   */
  goToOrders: function() {
    console.log('点击订单记录');
    wx.showToast({
      title: '正在跳转到订单页面...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/orders/orders',
        success: () => {
          console.log('跳转成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 1000);
  },

  /**
   * 显示会员查询界面
   */
  showMemberQuery: function() {
    console.log('点击会员查询');
    wx.showModal({
      title: '会员信息',
      content: '当前等级：月卡会员\n开通会员，享受更多权益',
      showCancel: true,
      cancelText: '关闭',
      confirmText: '续费',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '跳转到续费页面',
            icon: 'success',
            duration: 2000
          });
        }
      }
    });
  },

  /**
   * 跳转到排行查询页面
   */
  goToRanking: function() {
    console.log('点击排行查询');
    wx.showToast({
      title: '正在跳转到排行榜...',
      icon: 'loading',
      duration: 1000
    });

    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/lanhu_paihangbang/component',
        success: () => {
          console.log('跳转排行榜成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
    }, 1000);
  },

  /**
   * 显示WiFi连接功能界面
   */
  showWifiConnection: function() {
    console.log('点击WiFi链接');

    wx.showActionSheet({
      itemList: ['查看当前WiFi', '连接新WiFi', 'WiFi设置'],
      success: (res) => {
        const tapIndex = res.tapIndex;
        switch(tapIndex) {
          case 0:
            wx.showModal({
              title: '当前WiFi',
              content: 'WiFi名称：自习室WiFi\n信号强度：强',
              showCancel: false
            });
            break;
          case 1:
            wx.showToast({
              title: '正在搜索WiFi...',
              icon: 'loading',
              duration: 2000
            });
            break;
          case 2:
            wx.showToast({
              title: '跳转到WiFi设置',
              icon: 'success',
              duration: 2000
            });
            break;
        }
      },
      fail: (res) => {
        console.log('用户取消选择');
      }
    });
  }
});
