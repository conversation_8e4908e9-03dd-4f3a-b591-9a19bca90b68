Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 页面状态管理
    loading: false,
    error: null,

    // 排行榜切换状态
    activeRankingType: 'week', // 'day', 'week', 'month'
    rankingTypes: [
      { key: 'week', label: '周榜', active: true },
      { key: 'month', label: '月榜', active: false },
      { key: 'day', label: '日榜', active: false }
    ],

    // 用户点赞状态存储 - 格式: {userId: true/false}
    userLikeStatus: {},

    // 当前用户信息
    currentUser: {
      userId: 'U001',
      userName: '学习者123',
      avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNGb25511adc110cd9ea1af4e0a843b8775.png',
      currentSessionHours: 23, // 本次就座时长
      totalStudyHours: 23,    // 累计就座学时
      currentRank: 8           // 当前排名
    },

    // 排行榜数据 - 按不同时间维度
    rankingData: {
      day: {
        champion: {
          id: 1,
          rank: 1,
          name: '学霸小王',
          avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
          studyHours: 8,
          checkInDays: 1,
          points: 15,
          medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
        },
        list: [
          {
            id: 1, rank: 1, name: '学霸小王',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 8, checkInDays: 1, points: 15, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
          },
          {
            id: 2, rank: 2, name: '努力小李',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 7, checkInDays: 1, points: 12, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/yajun.png'
          },
          {
            id: 3, rank: 3, name: '勤奋小张',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 6, checkInDays: 1, points: 10, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/jijun.png'
          },
          {
            id: 4, rank: 4, name: '坚持小刘',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 5, checkInDays: 1, points: 8, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG380a41f5523e964018075c68600c040e.png'
          },
          {
            id: 5, rank: 5, name: '专注小陈',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 4, checkInDays: 1, points: 6, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG0a56686308069e8d96dc5cebc07350dc.png'
          },
          {
            id: 6, rank: 6, name: '认真小赵',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 3, checkInDays: 1, points: 4, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG8efe184474e3a46ed266bb7c5096c0f4.png'
          }
        ]
      },
      week: {
        champion: {
          id: 1,
          rank: 1,
          name: '悬溺123',
          avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
          studyHours: 45,
          checkInDays: 220,
          points: 85,
          medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
        },
        list: [
          {
            id: 1, rank: 1, name: '悬溺123',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 45, checkInDays: 7, points: 85, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
          },
          {
            id: 2, rank: 2, name: '学霸小王',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 38, checkInDays: 6, points: 72, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/yajun.png'
          },
          {
            id: 3, rank: 3, name: '努力小李',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 32, checkInDays: 5, points: 58, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/jijun.png'
          },
          {
            id: 4, rank: 4, name: '勤奋小张',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 28, checkInDays: 5, points: 45, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG380a41f5523e964018075c68600c040e.png'
          },
          {
            id: 5, rank: 5, name: '坚持小刘',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 25, checkInDays: 4, points: 38, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG0a56686308069e8d96dc5cebc07350dc.png'
          },
          {
            id: 6, rank: 6, name: '专注小陈',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 22, checkInDays: 4, points: 32, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG8efe184474e3a46ed266bb7c5096c0f4.png'
          },
          {
            id: 7, rank: 7, name: '专注小陈',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 22, checkInDays: 4, points: 32, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG8efe184474e3a46ed266bb7c5096c0f4.png'
          }
        ]
      },
      month: {
        champion: {
          id: 1,
          rank: 1,
          name: '超级学霸',
          avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
          studyHours: 180,
          checkInDays: 28,
          points: 350,
          medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
        },
        list: [
          {
            id: 1, rank: 1, name: '超级学霸',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 180, checkInDays: 28, points: 350, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/guanjun.png'
          },
          {
            id: 2, rank: 2, name: '月度之星',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 165, checkInDays: 26, points: 320, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/yajun.png'
          },
          {
            id: 3, rank: 3, name: '坚持达人',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 148, checkInDays: 24, points: 285, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/jijun.png'
          },
          {
            id: 4, rank: 4, name: '学习标兵',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 132, checkInDays: 22, points: 250, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG380a41f5523e964018075c68600c040e.png'
          },
          {
            id: 5, rank: 5, name: '努力青年',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 118, checkInDays: 20, points: 220, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG0a56686308069e8d96dc5cebc07350dc.png'
          },
          {
            id: 6, rank: 6, name: '勤学好问',
            avatar: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG9eb6a59079ebc189e21f702b6fd86955.png',
            studyHours: 105, checkInDays: 18, points: 195, isLiked: false,
            medalIcon: '../../images/lanhu_paihangbang/FigmaDDSSlicePNG8efe184474e3a46ed266bb7c5096c0f4.png'
          }
        ]
      }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.info("排行榜页面加载");
    this.initPage();
  },

  /**
   * 初始化页面数据
   */
  initPage: function () {
    // 加载本地存储的点赞状态
    this.loadLikeStatusFromStorage();
    // 设置当前排行榜数据
    this.updateCurrentRankingData();
  },

  /**
   * 更新当前显示的排行榜数据
   */
  updateCurrentRankingData: function () {
    const currentData = this.data.rankingData[this.data.activeRankingType];
    this.setData({
      currentChampion: currentData.champion,
      currentRankingList: currentData.list
    });
  },

  /**
   * 切换排行榜类型
   */
  switchRankingType: function (e) {
    const type = e.currentTarget.dataset.type;

    // 更新按钮状态
    const updatedTypes = this.data.rankingTypes.map(item => ({
      ...item,
      active: item.key === type
    }));

    this.setData({
      activeRankingType: type,
      rankingTypes: updatedTypes
    });

    // 更新排行榜数据
    this.updateCurrentRankingData();

    // 显示切换提示
    const typeLabel = this.data.rankingTypes.find(item => item.key === type).label;
    wx.showToast({
      title: `已切换到${typeLabel}`,
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 点赞功能
   */
  onLikeTap: function (e) {
    // 阻止事件冒泡
    e.stopPropagation();

    const userId = e.currentTarget.dataset.userId;
    console.log('点赞用户:', userId);

    // 获取当前用户的点赞状态
    const currentLikeStatus = this.data.userLikeStatus[userId] || false;
    const newLikeStatus = !currentLikeStatus;

    // 找到对应用户
    const currentData = this.data.rankingData[this.data.activeRankingType];
    const userIndex = currentData.list.findIndex(user => user.id === userId);

    if (userIndex !== -1) {
      const updatedList = [...currentData.list];
      const user = updatedList[userIndex];

      // 更新点赞状态和点赞数
      user.isLiked = newLikeStatus;
      if (newLikeStatus) {
        // 点赞：增加1
        user.points = (user.points || 0) + 1;
      } else {
        // 取消点赞：减少1，但不能小于0
        user.points = Math.max((user.points || 0) - 1, 0);
      }

      // 更新用户点赞状态记录
      const updatedLikeStatus = {
        ...this.data.userLikeStatus,
        [userId]: newLikeStatus
      };

      // 更新排行榜数据
      const updatedRankingData = {
        ...this.data.rankingData,
        [this.data.activeRankingType]: {
          ...currentData,
          list: updatedList
        }
      };

      this.setData({
        rankingData: updatedRankingData,
        currentRankingList: updatedList,
        userLikeStatus: updatedLikeStatus
      });

      // 显示操作反馈
      wx.showToast({
        title: newLikeStatus ? '点赞成功' : '取消点赞',
        icon: 'success',
        duration: 800
      });

      // 保存点赞状态到本地存储
      this.saveLikeStatusToStorage();
    }
  },

  /**
   * 保存点赞状态到本地存储
   */
  saveLikeStatusToStorage: function() {
    try {
      wx.setStorageSync('userLikeStatus', this.data.userLikeStatus);
    } catch (error) {
      console.error('保存点赞状态失败:', error);
    }
  },

  /**
   * 从本地存储加载点赞状态
   */
  loadLikeStatusFromStorage: function() {
    try {
      const savedLikeStatus = wx.getStorageSync('userLikeStatus');
      if (savedLikeStatus) {
        this.setData({
          userLikeStatus: savedLikeStatus
        });
        // 更新排行榜数据中的点赞状态
        this.updateRankingListLikeStatus();
      }
    } catch (error) {
      console.error('加载点赞状态失败:', error);
    }
  },

  /**
   * 更新排行榜列表中的点赞状态
   */
  updateRankingListLikeStatus: function() {
    const rankingData = { ...this.data.rankingData };
    const likeStatus = this.data.userLikeStatus;

    // 更新所有排行榜数据中的点赞状态
    Object.keys(rankingData).forEach(type => {
      rankingData[type].list.forEach(user => {
        user.isLiked = likeStatus[user.id] || false;
      });
    });

    this.setData({
      rankingData: rankingData
    });

    // 更新当前显示的排行榜
    this.updateCurrentRankingData();
  },

  /**
   * 刷新排行榜数据
   */
  refreshRanking: function () {
    this.setData({ loading: true });

    // 模拟API调用
    setTimeout(() => {
      this.loadRankingFromAPI();
    }, 1000);
  },

  /**
   * 从API加载排行榜数据
   * TODO: 替换为实际的API调用
   */
  loadRankingFromAPI: function () {
    try {
      // 这里应该是实际的API调用
      // const result = await wx.request({...});

      // 模拟数据更新
      this.updateCurrentRankingData();

      this.setData({
        loading: false,
        error: null
      });

      wx.showToast({
        title: '数据已更新',
        icon: 'success'
      });
    } catch (error) {
      console.error('加载排行榜数据失败:', error);
      this.setData({
        loading: false,
        error: '加载失败，请重试'
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.info("排行榜页面渲染完成");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.info("排行榜页面显示");
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    console.info("排行榜页面隐藏");
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.info("排行榜页面卸载");
  }
});
