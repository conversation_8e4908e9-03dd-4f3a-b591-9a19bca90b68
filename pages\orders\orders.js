Page({
  data: {
    currentFilter: 'all',
    refreshing: false,
    loading: false,
    hasMore: true,
    orders: [
      {
        id: 1,
        orderNumber: 'ORD202412110001',
        seatName: 'A区-01号座位',
        location: '一楼自习区',
        startTime: '2024-12-11 09:00',
        endTime: '2024-12-11 18:00',
        totalPrice: '45.00',
        status: 'completed',
        statusText: '已完成',
        createTime: '2024-12-11 08:30'
      },
      {
        id: 2,
        orderNumber: 'ORD202412110002',
        seatName: 'B区-15号座位',
        location: '二楼安静区',
        startTime: '2024-12-12 14:00',
        endTime: '2024-12-12 22:00',
        totalPrice: '40.00',
        status: 'paid',
        statusText: '已付款',
        createTime: '2024-12-11 13:45'
      },
      {
        id: 3,
        orderNumber: 'ORD202412110003',
        seatName: 'C区-08号座位',
        location: '三楼讨论区',
        startTime: '2024-12-13 10:00',
        endTime: '2024-12-13 16:00',
        totalPrice: '30.00',
        status: 'pending',
        statusText: '待付款',
        createTime: '2024-12-11 16:20'
      }
    ],
    filteredOrders: []
  },

  onLoad: function(options) {
    console.log('订单页面加载');
    this.filterOrders({ currentTarget: { dataset: { type: 'all' } } });
  },

  onShow: function() {
    console.log('订单页面显示');
  },

  /**
   * 筛选订单
   */
  filterOrders: function(e) {
    const type = e.currentTarget.dataset.type;
    const orders = this.data.orders;
    let filteredOrders = [];

    switch(type) {
      case 'all':
        filteredOrders = orders;
        break;
      case 'pending':
        filteredOrders = orders.filter(order => order.status === 'pending');
        break;
      case 'paid':
        filteredOrders = orders.filter(order => order.status === 'paid');
        break;
      case 'completed':
        filteredOrders = orders.filter(order => order.status === 'completed');
        break;
    }

    this.setData({
      currentFilter: type,
      filteredOrders: filteredOrders
    });
  },

  /**
   * 下拉刷新
   */
  onRefresh: function() {
    this.setData({
      refreshing: true
    });

    // 模拟刷新数据
    setTimeout(() => {
      this.setData({
        refreshing: false
      });
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    }, 1000);
  },

  /**
   * 加载更多
   */
  loadMore: function() {
    if (this.data.loading || !this.data.hasMore) return;

    this.setData({
      loading: true
    });

    // 模拟加载更多数据
    setTimeout(() => {
      this.setData({
        loading: false,
        hasMore: false
      });
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none',
        duration: 1000
      });
    }, 1000);
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function(e) {
    const order = e.currentTarget.dataset.order;
    console.log('查看订单详情:', order);
    
    wx.showModal({
      title: '订单详情',
      content: `订单号：${order.orderNumber}\n座位：${order.seatName}\n时间：${order.startTime} - ${order.endTime}\n金额：¥${order.totalPrice}\n状态：${order.statusText}`,
      showCancel: false,
      confirmText: '确定'
    });
  },

  /**
   * 支付订单
   */
  payOrder: function(e) {
    const order = e.currentTarget.dataset.order;
    console.log('支付订单:', order);
    
    wx.showModal({
      title: '确认支付',
      content: `订单金额：¥${order.totalPrice}`,
      confirmText: '立即支付',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '支付中...',
            mask: true
          });
          
          // 模拟支付过程
          setTimeout(() => {
            wx.hideLoading();
            
            // 更新订单状态
            const orders = this.data.orders;
            const index = orders.findIndex(item => item.id === order.id);
            if (index !== -1) {
              orders[index].status = 'paid';
              orders[index].statusText = '已付款';
              this.setData({
                orders: orders
              });
              this.filterOrders({ currentTarget: { dataset: { type: this.data.currentFilter } } });
            }
            
            wx.showToast({
              title: '支付成功',
              icon: 'success',
              duration: 2000
            });
          }, 2000);
        }
      }
    });
  },

  /**
   * 取消订单
   */
  cancelOrder: function(e) {
    const order = e.currentTarget.dataset.order;
    console.log('取消订单:', order);
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      confirmText: '确认取消',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...',
            mask: true
          });
          
          // 模拟取消过程
          setTimeout(() => {
            wx.hideLoading();
            
            // 移除订单
            const orders = this.data.orders.filter(item => item.id !== order.id);
            this.setData({
              orders: orders
            });
            this.filterOrders({ currentTarget: { dataset: { type: this.data.currentFilter } } });
            
            wx.showToast({
              title: '订单已取消',
              icon: 'success',
              duration: 2000
            });
          }, 1000);
        }
      }
    });
  },

  /**
   * 去预订座位
   */
  goToBooking: function() {
    wx.switchTab({
      url: '/pages/lanhu_shouye/component',
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
});
