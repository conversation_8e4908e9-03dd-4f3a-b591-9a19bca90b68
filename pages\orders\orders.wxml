<!-- 订单记录页面 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">订单记录</text>
  </view>

  <!-- 订单筛选 -->
  <view class="filter-bar">
    <view class="filter-item {{currentFilter === 'all' ? 'active' : ''}}" bindtap="filterOrders" data-type="all">
      <text>全部</text>
    </view>
    <view class="filter-item {{currentFilter === 'pending' ? 'active' : ''}}" bindtap="filterOrders" data-type="pending">
      <text>待付款</text>
    </view>
    <view class="filter-item {{currentFilter === 'paid' ? 'active' : ''}}" bindtap="filterOrders" data-type="paid">
      <text>已付款</text>
    </view>
    <view class="filter-item {{currentFilter === 'completed' ? 'active' : ''}}" bindtap="filterOrders" data-type="completed">
      <text>已完成</text>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view class="order-list" scroll-y="true" refresher-enabled="true" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
    <view wx:if="{{filteredOrders.length > 0}}">
      <view class="order-item" wx:for="{{filteredOrders}}" wx:key="id" bindtap="viewOrderDetail" data-order="{{item}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-number">订单号：{{item.orderNumber}}</text>
            <text class="order-date">{{item.createTime}}</text>
          </view>
          <view class="order-status {{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
        </view>

        <!-- 订单内容 -->
        <view class="order-content">
          <view class="seat-info">
            <text class="seat-name">{{item.seatName}}</text>
            <text class="seat-location">{{item.location}}</text>
          </view>
          <view class="time-info">
            <text class="time-label">使用时间：</text>
            <text class="time-range">{{item.startTime}} - {{item.endTime}}</text>
          </view>
        </view>

        <!-- 订单底部 -->
        <view class="order-footer">
          <view class="price-info">
            <text class="price-label">总计：</text>
            <text class="price">¥{{item.totalPrice}}</text>
          </view>
          <view class="order-actions">
            <button wx:if="{{item.status === 'pending'}}" class="action-btn pay-btn" bindtap="payOrder" data-order="{{item}}" catchtap="true">
              立即付款
            </button>
            <button wx:if="{{item.status === 'paid'}}" class="action-btn cancel-btn" bindtap="cancelOrder" data-order="{{item}}" catchtap="true">
              取消订单
            </button>
            <button class="action-btn detail-btn" bindtap="viewOrderDetail" data-order="{{item}}" catchtap="true">
              查看详情
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-state">
      <image class="empty-icon" src="/images/empty-order.png" mode="aspectFit"></image>
      <text class="empty-text">暂无订单记录</text>
      <button class="go-booking-btn" bindtap="goToBooking">去预订座位</button>
    </view>
  </scroll-view>

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && filteredOrders.length > 0}}" class="load-more" bindtap="loadMore">
    <text>{{loading ? '加载中...' : '点击加载更多'}}</text>
  </view>
</view>
