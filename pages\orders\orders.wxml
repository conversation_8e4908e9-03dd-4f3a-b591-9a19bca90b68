<!-- 订单记录页面 -->
<view class="container">
  <view class="filter-bar">
    <view class="filter-item {{currentFilter === 'all' ? 'active' : ''}}" bindtap="filterOrders" data-type="all">
      <text>全部</text>
    </view>
    <view class="filter-item {{currentFilter === 'pending' ? 'active' : ''}}" bindtap="filterOrders" data-type="pending">
      <text>待付款</text>
    </view>
    <view class="filter-item {{currentFilter === 'paid' ? 'active' : ''}}" bindtap="filterOrders" data-type="paid">
      <text>已付款</text>
    </view>
    <view class="filter-item {{currentFilter === 'completed' ? 'active' : ''}}" bindtap="filterOrders" data-type="completed">
      <text>已完成</text>
    </view>
  </view>

  <!-- 订单列表 -->
 

  <!-- 加载更多 -->
  <view wx:if="{{hasMore && filteredOrders.length > 0}}" class="load-more" bindtap="loadMore">
    <text>{{loading ? '加载中...' : '点击加载更多'}}</text>
  </view>
</view>
