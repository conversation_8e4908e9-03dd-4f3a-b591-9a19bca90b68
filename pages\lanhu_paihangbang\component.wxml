<view class="page">
  <view class="group_1">
    <view class="box_1">
      <view class="single-avatar_1" >
        <image class="image_1" ></image>
        <text lines="1" class="user-name-text">{{currentUser.userName}}</text>
      </view>
      
      <view class="group_2">
        <view class="section_1">
          <view class="text-wrapper_1">
            <text lines="1" class="label-text_1">本次就座时长</text>
            <text lines="1" class="text_1">{{currentUser.currentSessionHours}}h</text>
          </view>
          <view class="divider-line"></view>
          <view class="text-wrapper_2">
            <text lines="1" class="label-text_2">累计就座学时</text>
            <text lines="1" class="text_3">{{currentUser.totalStudyHours}}h</text>
          </view>
        </view>
      </view>
    </view>

    <view class="box_2">
      <!-- 排行榜切换按钮 -->
      <view class="ranking-tabs">
        <view class="tab-button {{item.active ? 'active' : ''}}"
              wx:for="{{rankingTypes}}"
              wx:key="key"
              bindtap="switchRankingType"
              data-type="{{item.key}}">
          <text class="tab-text">{{item.label}}</text>
        </view>
      </view>
      <view class="section_3">
        <!-- 排行榜列表 -->
        <view class="ranking-list">
          <view class="ranking-item"
                wx:for="{{currentRankingList}}"
                wx:key="id">

            <!-- 排名图标 -->
            <view class="rank-icon-container">
              <view class="rank-icon rank-{{item.rank}}" wx:if="{{item.rank <= 3}}">
                <text class="rank-number-top">{{item.rank}}</text>
                <image src="{{item.medalIcon}}" class="medal-icon" wx:if="{{item.medalIcon}}"></image>
              </view>
              <view class="rank-number" wx:else>{{item.rank}}</view>
            </view>

            <!-- 用户信息 -->
            <view class="user-info">
              <image src="{{item.avatar}}" class="user-avatar"></image>
              <view class="user-details">
                <text class="user-name">{{item.name}}</text>
                <text class="study-time">学时:{{item.studyHours}}h   <text>打卡天数:{{item.checkInDays}}天</text>
                </text>
              </view>
            </view>

            <!-- 点赞按钮 -->
            <view class="like-button" bindtap="onLikeTap" data-user-id="{{item.id}}">
              <image src="{{item.isLiked ? '../../images/lanhu_paihangbang/dainzan.png' : '../../images/lanhu_paihangbang/点赞.png'}}" class="like-icon"></image>
              <text class="like-count">{{item.points || 0}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="section_4">
        <view class="block_2"
              wx:for="{{currentRankingList}}"
              wx:key="id">
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error-container" wx:if="{{error}}">
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="refreshRanking">重试</button>
  </view>
</view>